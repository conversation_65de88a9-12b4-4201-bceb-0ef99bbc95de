---
title: エラーコード
description: Kantokuのエラーコードの理解と解決。
order: 2
---

# エラーコードリファレンス

![Error Codes](/figma-designs/instruction-12.png)

このリファレンスガイドは、一般的なエラーコードの理解と解決に役立ちます。

## 認証エラー

### エラー 401: 未承認
**原因:** 無効または期限切れの認証情報
**解決策:** 
- ログアウトして再度ログイン
- ユーザー名とパスワードを確認
- アカウントがロックされている場合は管理者に連絡

### エラー 403: 禁止
**原因:** 権限不足
**解決策:**
- 管理者に連絡
- ユーザーロールと権限を確認

## 接続エラー

### エラー 500: 内部サーバーエラー
**原因:** サーバー側の問題
**解決策:**
- 数分待ってから再試行
- エラーが続く場合はサポートに連絡

### エラー 503: サービス利用不可
**原因:** サーバーメンテナンスまたは過負荷
**解決策:**
- ステータスページを確認
- 後で再試行
- 更新についてはサポートに連絡

ここに記載されていない追加のエラーコードについては、技術サポートチームにお問い合わせください。
